import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/api/analyze_api.dart';
import 'package:police/common/dialog.dart';
import 'package:police/common/model/analyze_details_model/analyze_details_model.dart';
import 'package:police/common/model/analyze_model.dart';

import '../analyze/controller.dart';

class AnalyzedetailController extends GetxController {
  int code = 0;
  AnalyzeDetailsModel details = AnalyzeDetailsModel();

  /// 获取研判详情
  Future<AnalyzeDetailsModel> getAnalyzeDetails(int id) async {
    code = 0;
    update(['details']);

    details = await AnalyzeApi.getAnalyzeDetails(
      id: id,
      fail: (result) {
        code = 404;
        update(['details']);
      },
    );
    code = 200;
    update(['details']);
    return details;
  }

  /// 新增研判
  Future<AnalyzeModel?> createAnalyze(BuildContext context,
      {required AnalyzeModel analyze}) async {
    Dialogs.showLoadingDialog(context);

    AnalyzeModel result = await AnalyzeApi.createAnalyze(
      type: analyze.types!,
      date: analyze.yearMonthDay ?? '',
      details: analyze.graphics ?? '',
      fail: (result) {
        return;
      },
    );
    return result;
  }

  /// 修改研判
  Future<AnalyzeModel?> updataAnalyze(BuildContext context,
      {required AnalyzeModel analyze}) async {
    Dialogs.showLoadingDialog(context);

    AnalyzeModel result = await AnalyzeApi.updataAnalyze(
      id: analyze.id!,
      details: analyze.graphics ?? '',
      fail: (result) {
        // showToast('保存失败');
        return;
      },
    );
    showToast('保存成功');
    return result;
  }

  /// 获取第几季度
  String getQuarter(AnalyzeModel detail) {
    String month = getMonth(detail);
    switch (month) {
      case '01' || '1' || '02' || '2' || '03' || '3':
        return '一';
      case '04' || '4' || '05' || '5' || '06' || '6':
        return '二';
      case '07' || '7' || '08' || '8' || '09' || '9':
        return '三';
      default:
        return '四';
    }
  }

  String getYear(AnalyzeModel detail) => detail.yearMonthDay!.split('-')[0];

  String getMonth(AnalyzeModel detail) => detail.yearMonthDay!.split('-')[1];

  String getDay(AnalyzeModel detail) => detail.yearMonthDay!.split('-')[2];

  /// 获取年月
  String getYearMonth(AnalyzeModel detail) {
    AnalyzedetailController controller = Get.find<AnalyzedetailController>();
    String str = '';

    switch (detail.types) {
      case AnalyzeSortTypes.month:
        str =
            '${detail.yearMonthDay!.split('-')[0]}年${detail.yearMonthDay!.split('-')[1]}月';

        break;
      case AnalyzeSortTypes.quarter:
        str =
            '(${detail.yearMonthDay!.split('-')[0]})第${controller.getQuarter(detail)}季度';

        break;
      case AnalyzeSortTypes.year:
        str = '${detail.yearMonthDay!.split('-')[0]}年';

        break;
      default:
    }

    return str;
  }
}
