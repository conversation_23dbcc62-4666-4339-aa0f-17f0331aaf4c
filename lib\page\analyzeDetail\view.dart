import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/routes/jump_page.dart';
import 'package:police/common/widget/http_error_widget.dart';
import 'package:police/common/widget/loading_widget.dart';
import 'package:police/common/widget/scaffold_widget.dart';
import 'package:police/page/analyze/controller.dart';
import 'package:police/page/analyzeDetail/widgets/analyze_detail.dart';
import 'package:police/page/analyzeDetail/widgets/analyze_intro.dart';
import 'package:police/page/analyzeDetail/widgets/analyze_table.dart';
import 'package:police/page/analyzeDetail/widgets/area_chart.dart';
import 'package:police/page/analyzeDetail/widgets/pie_charts.dart';

import '../../common/widget/corner_card.dart';
import '../../common/widget/float_square_button.dart';
import 'index.dart';
import 'widgets/day_chart.dart';

class AnalyzedetailPage extends GetView<AnalyzedetailController> {
  const AnalyzedetailPage(this.detail, {super.key});

  final AnalyzeModel detail;

  // 主视图
  Widget _buildView(BuildContext context) {
    return GetBuilder<AnalyzedetailController>(
        id: 'details',
        builder: (contro) {
          return controller.code == 0 && detail.id != null
              ? const LoadingWidget()
              : controller.code == 404
                  ? const HttpErrorWidget()
                  : SingleChildScrollView(
                      child: Center(
                        child: SizedBox(
                          width: Get.width * .7,
                          child: Padding(
                            padding: const EdgeInsets.all(60),
                            child: Column(
                              children: [
                                CornerCard(
                                  backgroundColor: const Color(0xca073773),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 30, vertical: 10),
                                  child: Column(
                                    children: [
                                      detail.id != null
                                          ? Column(
                                              children: [
                                                AnalyzeIntro(
                                                    detail: detail,
                                                    controller: controller),
                                                const Divider(
                                                    color: Colors.red),
                                              ],
                                            )
                                          : Container(),
                                      AnalyzeDetails(
                                          detail: detail,
                                          controller: controller),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 40),
                                detail.id != null &&
                                        detail.types != AnalyzeSortTypes.day
                                    ? AnalyzeTable(
                                        detail: detail, controller: controller)
                                    : Container(),
                                detail.id != null &&
                                        detail.types == AnalyzeSortTypes.month
                                    ? DayChart(
                                        detail: detail, controller: controller)
                                    : Container(),
                                const SizedBox(height: 40),
                                detail.id != null
                                    ? PieCharts(
                                        controller: controller, details: detail)
                                    : Container(),
                                const SizedBox(height: 40),
                                detail.id != null
                                    ? AreaChart(details: detail)
                                    : Container(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
        });
  }

  @override
  Widget build(BuildContext context) {
    if (detail.id != null) {
      Get.put(AnalyzedetailController()).getAnalyzeDetails(detail.id!);
    }

    return GetBuilder<AnalyzedetailController>(
      init: AnalyzedetailController(),
      id: "analyzedetail",
      builder: (_) {
        return ScaffoldWidget(
          body: _buildView(context),
          floatButton: [
            Padding(
              padding: const EdgeInsets.all(36),
              child: FloatSquareButton(
                onTap: () async {
                  if (detail.yearMonthDay == null) {
                    showToast('日期不能为空');
                    return;
                  }
                  try {
                    /// 是否为原始完整的正确格式
                    if (detail.yearMonthDay!.split('-').length - 1 >= 2) {
                    }
                    // 年
                    else if (detail.types == "year") {
                      detail.yearMonthDay = '${detail.yearMonthDay}-12-01';
                    }
                    // 月
                    else if (detail.types == "month") {
                      detail.yearMonthDay = '${detail.yearMonthDay}-01';
                    }
                    // 季度
                    else if (detail.types == "quarter") {
                      List<String> parts = detail.yearMonthDay!.split('-');
                      String strResult = parts[1]; // 获取 "-" 后面的字符串
                      // 使用 match case 结构
                      switch (strResult) {
                        case "1":
                          detail.yearMonthDay = "${parts[0]}-01-01";
                          break;
                        case "2":
                          detail.yearMonthDay = "${parts[0]}-04-01";
                          break;
                        case "3":
                          detail.yearMonthDay = "${parts[0]}-07-01";
                          break;
                        case "4":
                          detail.yearMonthDay =
                              "${parts[0]}-10-01"; // 这里修正为 10 月
                          break;
                        default:
                          showToast('日期格式错误');
                          return;
                      }
                    }
                  } catch (e) {
                    showToast('处理日期时发生错误');
                    return;
                  }

                  if (detail.id == null) {
                    final result = await controller.createAnalyze(context,
                        analyze: detail);
                    pageback(result: result);
                  } else {
                    await controller.updataAnalyze(context, analyze: detail);
                  }
                },
                icon: Icons.save,
              ),
            ),
          ],
        );
      },
    );
  }
}
