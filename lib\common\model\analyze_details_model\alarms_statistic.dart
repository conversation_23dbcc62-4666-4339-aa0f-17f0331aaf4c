import 'sort.dart';

class AlarmsStatistic {
  String? name;
  int? alarmCount;
  List<Sort>? sort;
  double? percent;

  AlarmsStatistic({
    this.name,
    this.alarmCount,
    List<Sort>? sort,
    this.percent,
  }) : sort = sort ?? <Sort>[];

  factory AlarmsStatistic.fromJson(Map<String, dynamic> json) {
    return AlarmsStatistic(
      name: json['name'] as String?,
      alarmCount: json['alarm_count'] as int?,
      sort: (json['sort'] as List<dynamic>?)
              ?.map((e) => Sort.fromJson(e as Map<String, dynamic>))
              .toList() ??
          <Sort>[],
      percent: json['percent'] as double?,
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'alarm_count': alarmCount,
        'sort': sort?.map((e) => e.toJson()).toList(),
        'percent': percent,
      };
}
