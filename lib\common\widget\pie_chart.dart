import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';

class <PERSON><PERSON><PERSON> extends StatelessWidget {
  final List<charts.Series<dynamic, String>> seriesList;
  final bool animate;
  final double? width;
  final double height;
  final bool showBehavior;
  final Function<bool>()? showLabel;

  const PieChart(
    this.seriesList, {
    super.key,
    this.animate = true,
    this.width,
    this.height = 270,
    this.showBehavior = false,
    this.showLabel,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? MediaQuery.of(context).size.width,
      height: height,
      child: charts.PieChart<String>(
        seriesList,
        animate: animate,
        defaultRenderer: charts.ArcRendererConfig<String>(
          arcRendererDecorators: [
            if (showLabel?.call() ?? false)
              charts.ArcLabelDecorator<String>(
                labelPosition: charts.ArcLabelPosition.outside,
                // insideLabelStyleSpec: const charts.TextStyleSpec(
                //   fontSize: 12,
                //   color: charts.MaterialPalette.black,
                // ),
                outsideLabelStyleSpec: const charts.TextStyleSpec(
                  fontSize: 12,
                  color: charts.MaterialPalette.white,
                ),
              ),
          ],
        ),
        behaviors: showBehavior
            ? [
                charts.DatumLegend(
                  entryTextStyle: const charts.TextStyleSpec(
                    fontSize: 12,
                    color: charts.MaterialPalette.white,
                  ),
                ),
              ]
            : [],
      ),
    );
  }
}
