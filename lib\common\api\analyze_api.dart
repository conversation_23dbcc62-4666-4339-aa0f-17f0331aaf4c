import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'package:police/common/model/analyze_details_model/analyze_details_model.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/api/apis.dart';
import 'package:police/common/util/https.dart';
import 'package:police/page/analyze/index.dart';

class AnalyzeApi {
  /// 获取研判列表
  static Future<List<AnalyzeModel>> getAnalyzeList({
    int page = 1,
    required String types,
    required Function(Response? result) fail,
  }) async {
    List<AnalyzeModel> analysis = [];

    Response response = await Https.get(
      Apis.analyze,
      queryParameters: {'page': page, 'types': types},
      fail: fail,
    );

    for (var item in response.data['items']) {
      analysis.add(AnalyzeModel.fromJson(item));
    }

    Get.find<AnalyzeController>().maxpages = response.data['pages'];

    return analysis;
  }

  /// 获取研判详情
  static Future<AnalyzeDetailsModel> getAnalyzeDetails({
    required int id,
    required Function(Response? result) fail,
  }) async {
    Response response = await Https.get(
      Apis.analyzeDetails,
      queryParameters: {'judgment_id': id},
      fail: fail,
    );

    return AnalyzeDetailsModel.fromJson(response.data);
  }

  /// 创建研判
  static Future<AnalyzeModel> createAnalyze({
    required String type,
    required String date,
    required String details,
    required Function(Response? result) fail,
  }) async {
    Response response = await Https.post(
      Apis.analyze,
      data: {"types": type, "year_month_day": date, "graphics": details},
      fail: fail,
      pageBack: true,
    );

    return AnalyzeModel.fromJson(response.data);
  }

  /// 修改研判
  static Future<AnalyzeModel> updataAnalyze({
    required int id,
    required String details,
    required Function(Response? result) fail,
  }) async {
    Response response = await Https.patch(
      '${Apis.analyze}/$id',
      data: {"graphics": details},
      fail: fail,
      pageBack: true,
    );

    return AnalyzeModel.fromJson(response.data);
  }

  /// 删除研判
  static deleteAnalyze({
    required int id,
    required Function(Response? result) fail,
  }) async {
    await Https.delete('${Apis.analyze}/$id', fail: fail, pageBack: true);
  }
}
